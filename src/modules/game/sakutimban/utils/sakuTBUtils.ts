import { 
  GameQuizQuestionAPI,
  OptionItem,
  CorrectPair,
  SakuTBItem,
  SakuTBQuestion,
  ParsedOptions,
  ParsedCorrectAnswers,
  SakuTBError
} from '../types/sakuTBTypes';

/**
 * Shuffle array function
 */
export const shuffleArray = <T>(array: T[]): T[] => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

/**
 * Parse Options JSON string thành array of OptionItem
 * @param optionsJson JSON string chứa 10 items
 * @returns ParsedOptions
 */
export const parseOptions = (optionsJson: string): ParsedOptions => {
  try {
    const options: string[] = JSON.parse(optionsJson);
    
    if (!Array.isArray(options) || options.length !== 10) {
      throw new Error(`Invalid options format. Expected 10 items, got ${options.length}`);
    }

    const leftItems: OptionItem[] = [];
    const rightItems: OptionItem[] = [];

    options.forEach((option, index) => {
      const item: OptionItem = {
        id: `option-${index + 1}`,
        text: option.trim(),
        type: detectItemType(option),
        index: index + 1
      };

      // 5 items đầu (index 1-5) là bên trái
      if (index < 5) {
        leftItems.push(item);
      } else {
        // 5 items còn lại (index 6-10) là bên phải
        rightItems.push(item);
      }
    });

    return { leftItems, rightItems };
  } catch (error) {
    console.error('[parseOptions] Error parsing options:', error);
    throw new Error(`Failed to parse options: ${error.message}`);
  }
};

/**
 * Parse CorrectAnswerIndex JSON string thành array of CorrectPair
 * @param correctAnswerJson JSON string dạng ["1,6","2,7","3,8","4,9","5,10"]
 * @returns ParsedCorrectAnswers
 */
export const parseCorrectAnswers = (correctAnswerJson: string): ParsedCorrectAnswers => {
  try {
    const correctAnswers: string[] = JSON.parse(correctAnswerJson);
    
    if (!Array.isArray(correctAnswers) || correctAnswers.length !== 5) {
      throw new Error(`Invalid correct answers format. Expected 5 pairs, got ${correctAnswers.length}`);
    }

    const pairs: CorrectPair[] = correctAnswers.map((pair, index) => {
      const [leftIndex, rightIndex] = pair.split(',').map(Number);
      
      // Validate indices
      if (isNaN(leftIndex) || isNaN(rightIndex)) {
        throw new Error(`Invalid pair format: ${pair}`);
      }
      
      if (leftIndex < 1 || leftIndex > 5) {
        throw new Error(`Invalid left index: ${leftIndex}. Must be 1-5`);
      }
      
      if (rightIndex < 6 || rightIndex > 10) {
        throw new Error(`Invalid right index: ${rightIndex}. Must be 6-10`);
      }

      return {
        leftIndex,
        rightIndex,
        matchId: `match-${index + 1}`
      };
    });

    return { pairs };
  } catch (error) {
    console.error('[parseCorrectAnswers] Error parsing correct answers:', error);
    throw new Error(`Failed to parse correct answers: ${error.message}`);
  }
};

/**
 * Detect item type based on content
 * @param content Item content
 * @returns Item type
 */
export const detectItemType = (content: string): 'text' | 'audio' | 'image' => {
  const trimmedContent = content.trim().toLowerCase();
  
  // Check for audio URLs
  if (trimmedContent.includes('.mp3') || 
      trimmedContent.includes('.wav') || 
      trimmedContent.includes('.m4a') ||
      trimmedContent.includes('audio')) {
    return 'audio';
  }
  
  // Check for image URLs
  if (trimmedContent.includes('.jpg') || 
      trimmedContent.includes('.jpeg') || 
      trimmedContent.includes('.png') || 
      trimmedContent.includes('.gif') ||
      trimmedContent.includes('.webp') ||
      trimmedContent.includes('image')) {
    return 'image';
  }
  
  // Default to text
  return 'text';
};

/**
 * Transform GameQuizQuestionAPI thành SakuTBQuestion
 * @param rawQuestion Raw question from API
 * @returns SakuTBQuestion
 */
export const transformQuestion = (rawQuestion: GameQuizQuestionAPI): SakuTBQuestion => {
  try {
    // Parse options
    const { leftItems, rightItems } = parseOptions(rawQuestion.Options);
    
    // Parse correct answers
    const { pairs } = parseCorrectAnswers(rawQuestion.CorrectAnswerIndex);
    
    // Transform to SakuTBItems
    const leftSakuItems: SakuTBItem[] = leftItems.map(item => {
      // Find matching pair for this left item
      const matchingPair = pairs.find(pair => pair.leftIndex === item.index);
      
      return {
        id: `left-${rawQuestion.Id}-${item.index}`,
        text: item.text,
        type: item.type,
        matchId: matchingPair ? matchingPair.matchId : `no-match-${item.index}`,
        side: 'left',
        originalIndex: item.index
      };
    });
    
    const rightSakuItems: SakuTBItem[] = rightItems.map(item => {
      // Find matching pair for this right item
      const matchingPair = pairs.find(pair => pair.rightIndex === item.index);
      
      return {
        id: `right-${rawQuestion.Id}-${item.index}`,
        text: item.text,
        type: item.type,
        matchId: matchingPair ? matchingPair.matchId : `no-match-${item.index}`,
        side: 'right',
        originalIndex: item.index
      };
    });

    return {
      id: rawQuestion.Id,
      content: rawQuestion.Content,
      leftItems: shuffleArray(leftSakuItems),
      rightItems: shuffleArray(rightSakuItems),
      correctPairs: pairs
    };
  } catch (error) {
    console.error('[transformQuestion] Error transforming question:', error);
    throw new Error(`Failed to transform question ${rawQuestion.Id}: ${error.message}`);
  }
};

/**
 * Transform multiple questions
 * @param rawQuestions Array of raw questions
 * @returns Array of transformed questions
 */
export const transformQuestions = (rawQuestions: GameQuizQuestionAPI[]): SakuTBQuestion[] => {
  const transformedQuestions: SakuTBQuestion[] = [];
  const errors: string[] = [];

  rawQuestions.forEach((rawQuestion, index) => {
    try {
      const transformed = transformQuestion(rawQuestion);
      transformedQuestions.push(transformed);
    } catch (error) {
      console.error(`[transformQuestions] Error transforming question ${index + 1}:`, error);
      errors.push(`Question ${index + 1}: ${error.message}`);
    }
  });

  if (errors.length > 0) {
    console.warn(`[transformQuestions] ${errors.length} questions failed to transform:`, errors);
  }

  console.log(`[transformQuestions] Successfully transformed ${transformedQuestions.length}/${rawQuestions.length} questions`);
  return transformedQuestions;
};

/**
 * Validate transformed question
 * @param question Transformed question
 * @returns boolean
 */
export const validateTransformedQuestion = (question: SakuTBQuestion): boolean => {
  try {
    // Check basic structure
    if (!question.id || !question.content || !question.leftItems || !question.rightItems || !question.correctPairs) {
      return false;
    }

    // Check items count
    if (question.leftItems.length !== 5 || question.rightItems.length !== 5) {
      return false;
    }

    // Check correct pairs count
    if (question.correctPairs.length !== 5) {
      return false;
    }

    // Check all items have valid matchIds
    const allItems = [...question.leftItems, ...question.rightItems];
    const hasInvalidMatchId = allItems.some(item => !item.matchId || item.matchId.startsWith('no-match'));
    
    return !hasInvalidMatchId;
  } catch (error) {
    console.error('[validateTransformedQuestion] Validation error:', error);
    return false;
  }
};

/**
 * Generate unique ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Format time display
 */
export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Create error object
 */
export const createError = (
  type: SakuTBError['type'], 
  message: string, 
  details?: any
): SakuTBError => {
  return { type, message, details };
};

/**
 * Log question structure for debugging
 */
export const logQuestionStructure = (question: SakuTBQuestion): void => {
  console.log(`[Question ${question.id}] Content: ${question.content}`);
  console.log('Left items:', question.leftItems.map(item => `${item.originalIndex}: ${item.text} (${item.matchId})`));
  console.log('Right items:', question.rightItems.map(item => `${item.originalIndex}: ${item.text} (${item.matchId})`));
  console.log('Correct pairs:', question.correctPairs.map(pair => `${pair.leftIndex} -> ${pair.rightIndex} (${pair.matchId})`));
};
