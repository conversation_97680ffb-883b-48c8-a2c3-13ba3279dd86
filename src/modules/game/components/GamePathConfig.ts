// Dynamic Game Path Configuration System

export interface MilestonePosition {
  id: number;
  top: number;
  left: number;
  levelName?: string;
}

export interface GamePathConfig {
  gameId: string;
  milestonePositions: MilestonePosition[];
  pathStyle: 'curved' | 'zigzag' | 'spiral' | 'linear' | 'wave';
  pathConfig?: {
    curvature?: number; // 0-1, độ cong của đường
    smoothness?: number; // Độ mượt
    direction?: 'clockwise' | 'counterclockwise';
    amplitude?: number; // Cho wave path
  };
  // Custom SVG path nếu cần
  customPathData?: string;
}

// Game-specific path configurations
export const GAME_PATH_CONFIGS: Record<string, GamePathConfig> = {
  'cf86bc33ef03447fa744eea2bbf31cfc': {
    gameId: 'cf86bc33ef03447fa744eea2bbf31cfc',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.75, left: 0.5, levelName: '<PERSON><PERSON> bản'},
      {id: 2, top: 0.68, left: 0.7, levelName: '<PERSON><PERSON>'},
      {id: 3, top: 0.59, left: 0.55, levelName: 'Trung bình'},
      {id: 4, top: 0.57, left: 0.37, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.4, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.5, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },

  '7eb45b0edc6247c3bde6bbb15547dfda': {
    gameId: '7eb45b0edc6247c3bde6bbb15547dfda',
    pathStyle: 'curved',
    milestonePositions: [
      {id: 1, top: 0.75, left: 0.5, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.7, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.55, levelName: 'Trung bình'},
      {id: 4, top: 0.57, left: 0.37, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.4, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.5, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.6,
      smoothness: 0.8,
      direction: 'clockwise',
    },
  },

  'DHBC': {
    gameId: 'DHBC',
    pathStyle: 'spiral',
    milestonePositions: [
      {id: 1, top: 0.9, left: 0.5, levelName: 'Bắt đầu'},
      {id: 2, top: 0.75, left: 0.8, levelName: 'Dễ'},
      {id: 3, top: 0.6, left: 0.2, levelName: 'Trung bình'},
      {id: 4, top: 0.45, left: 0.7, levelName: 'Khó'},
      {id: 5, top: 0.3, left: 0.3, levelName: 'Rất khó'},
      {id: 6, top: 0.15, left: 0.6, levelName: 'Siêu khó'},
      {id: 7, top: 0.05, left: 0.5, levelName: 'Thần thánh'},
    ],
    pathConfig: {
      curvature: 0.8,
      smoothness: 0.9,
      direction: 'counterclockwise',
    },
  },

  // Thêm config cho các game khác
  'SAKULC': {
    gameId: 'SAKULC',
    pathStyle: 'wave',
    milestonePositions: [
      {id: 1, top: 0.85, left: 0.3, levelName: 'Khởi đầu'},
      {id: 2, top: 0.7, left: 0.7, levelName: 'Cơ bản'},
      {id: 3, top: 0.55, left: 0.2, levelName: 'Nâng cao'},
      {id: 4, top: 0.4, left: 0.8, levelName: 'Khó'},
      {id: 5, top: 0.25, left: 0.4, levelName: 'Chuyên gia'},
      {id: 6, top: 0.1, left: 0.6, levelName: 'Bậc thầy'},
    ],
    pathConfig: {
      curvature: 0.5,
      smoothness: 0.7,
      amplitude: 0.3,
    },
  },
};

// Path generators for different styles
export class GamePathGenerator {
  static generateCurvedPath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    let pathData = "";
    const curvature = config?.curvature || 0.6;

    sortedPositions.forEach((position, index) => {
      if (index === 0) {
        pathData += `M ${position.left} ${position.top}`;
      } else if (index === 1) {
        const prev = sortedPositions[index - 1];
        const midX = (prev.left + position.left) / 2;
        const midY = (prev.top + position.top) / 2;

        const offsetX = (position.left - prev.left) * curvature * 0.3;
        const offsetY = (position.top - prev.top) * curvature * 0.2;

        const controlX = midX + offsetX;
        const controlY = midY - Math.abs(offsetY);

        pathData += ` Q ${controlX} ${controlY} ${position.left} ${position.top}`;
      } else {
        const prev = sortedPositions[index - 1];
        const next = sortedPositions[index + 1];

        const deltaX = position.left - prev.left;
        const deltaY = position.top - prev.top;

        const cp1X = prev.left + deltaX * 0.4 * curvature;
        const cp1Y = prev.top + deltaY * 0.2 * curvature;

        let cp2X, cp2Y;
        if (next) {
          const nextDeltaX = next.left - position.left;
          const nextDeltaY = next.top - position.top;
          cp2X = position.left - nextDeltaX * 0.3 * curvature;
          cp2Y = position.top - nextDeltaY * 0.2 * curvature;
        } else {
          cp2X = position.left - deltaX * 0.3 * curvature;
          cp2Y = position.top - deltaY * 0.1 * curvature;
        }

        pathData += ` C ${cp1X} ${cp1Y} ${cp2X} ${cp2Y} ${position.left} ${position.top}`;
      }
    });

    return pathData;
  }

  static generateZigzagPath(positions: MilestonePosition[]): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    let pathData = `M ${sortedPositions[0].left} ${sortedPositions[0].top}`;

    for (let i = 1; i < sortedPositions.length; i++) {
      pathData += ` L ${sortedPositions[i].left} ${sortedPositions[i].top}`;
    }

    return pathData;
  }

  static generateSpiralPath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const direction = config?.direction || 'clockwise';
    const curvature = config?.curvature || 0.8;

    // Implementation for spiral path
    return this.generateCurvedPath(positions, { curvature });
  }

  static generateWavePath(positions: MilestonePosition[], config?: any): string {
    if (positions.length === 0) return "";

    const sortedPositions = [...positions].sort((a, b) => a.id - b.id);
    const amplitude = config?.amplitude || 0.3;
    const curvature = config?.curvature || 0.5;

    let pathData = `M ${sortedPositions[0].left} ${sortedPositions[0].top}`;

    for (let i = 1; i < sortedPositions.length; i++) {
      const prev = sortedPositions[i - 1];
      const current = sortedPositions[i];

      // Create wave-like curves between points
      const midX = (prev.left + current.left) / 2;
      const midY = (prev.top + current.top) / 2;

      // Add wave oscillation
      const waveOffset = Math.sin(i * Math.PI) * amplitude * curvature;
      const controlX = midX + waveOffset;
      const controlY = midY;

      pathData += ` Q ${controlX} ${controlY} ${current.left} ${current.top}`;
    }

    return pathData;
  }
}

// Utility to get game path config
export const getGamePathConfig = (gameId: string): GamePathConfig => {
  const config = GAME_PATH_CONFIGS[gameId];
  if (!config) {
    throw new Error(`Game path config not found for gameId: ${gameId}`);
  }
  return config;
};

// Generate path data for a game
export const generateGamePath = (gameId: string): string => {
  const config = getGamePathConfig(gameId);

  if (config.customPathData) {
    return config.customPathData;
  }

  switch (config.pathStyle) {
    case 'curved':
      return GamePathGenerator.generateCurvedPath(config.milestonePositions, config.pathConfig);
    case 'zigzag':
      return GamePathGenerator.generateZigzagPath(config.milestonePositions);
    case 'spiral':
      return GamePathGenerator.generateSpiralPath(config.milestonePositions, config.pathConfig);
    case 'wave':
      return GamePathGenerator.generateWavePath(config.milestonePositions, config.pathConfig);
    case 'linear':
      return GamePathGenerator.generateZigzagPath(config.milestonePositions);
    default:
      return GamePathGenerator.generateCurvedPath(config.milestonePositions, config.pathConfig);
  }
};
