// Actual Generic Game Home Screen Component
import React, {useRef, useState} from 'react';
import {
  TouchableWithoutFeedback,
  View,
  Dimensions,
  Animated,
  PixelRatio,
  Platform,
} from 'react-native';
import {Text} from 'react-native-paper';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../../Config/ConfigAPI';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';

// Generic Game Home Screen Configuration Interface
export interface GameConfig {
  // Basic Info
  gameId: string;
  gameName: string;

  // Assets
  backgroundImage: any;
  milestoneImages: {
    completed: any;
    inProgress: any;
    locked: any;
  };
  birdImage?: any;
  footerIcons: {
    coin: any;
    rank: any;
    level: any;
    help: any;
  };

  // Content
  modalContent: string;
  helpText: string;

  // Navigation
  startGameScreen: string;
  startGameComponent?: React.ComponentType<any>;

  // Styling
  colors: {
    primary: string;
    footer: string;
    text: string;
  };

  // Game-specific Logic
  milestonePositions?: Array<{
    id: number;
    top: number;
    left: number;
  }>;

  // Custom hooks (optional)
  useSVGPath?: (params: any) => any;
  usePathTrail?: (params: any) => any;
}

// Default configurations for different games
export const GAME_CONFIGS: Record<string, GameConfig> = {
  cf86bc33ef03447fa744eea2bbf31cfc: {
    gameId: ConfigAPI.gameALTP,
    gameName: 'Ai là triệu phú',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'),
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Vượt qua 15 câu \n với 4 sự trợ giúp',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Trả lời đúng các câu hỏi để mở khóa các chặng tiếp theo.',
    startGameScreen: 'StartALTP',
    colors: {
      primary: '#112164',
      footer: '#FF5A5F',
      text: '#fff',
    },
  },

  '7eb45b0edc6247c3bde6bbb15547dfda': {
    gameId: ConfigAPI.gameSakuTB,
    gameName: 'Saku Tìm Bạn',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Ghép đôi các từ \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Ghép đôi các từ đúng để hoàn thành chặng.',
    startGameScreen: 'StartSakuTB',
    colors: {
      primary: '#2E7D32',
      footer: '#4CAF50',
      text: '#fff',
    },
  },

  DHBC: {
    gameId: 'DHBC',
    gameName: 'Đuổi Hình Bắt Chữ',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP assets
    milestoneImages: {
      completed: require('../ailatrieuphu/assets/pass.png'),
      inProgress: require('../ailatrieuphu/assets/Inpro.png'),
      locked: require('../ailatrieuphu/assets/New.png'),
    },
    birdImage: require('../ailatrieuphu/assets/bird-step.png'),
    footerIcons: {
      coin: require('../ailatrieuphu/assets/coin-icon.png'),
      rank: require('../ailatrieuphu/assets/rank.png'),
      level: require('../ailatrieuphu/assets/level.png'),
      help: require('../ailatrieuphu/assets/hd.png'),
    },
    modalContent: 'Đoán từ từ hình ảnh \n trong thời gian quy định',
    helpText:
      'Chọn một chặng để bắt đầu chơi. Đoán từ đúng từ hình ảnh để hoàn thành chặng.',
    startGameScreen: 'StartDHBC',
    colors: {
      primary: '#7B1FA2',
      footer: '#9C27B0',
      text: '#fff',
    },
  },

  // Có thể thêm các game khác...
};

// Props interface for Generic Home Screen
export interface GenericGameHomeScreenProps {
  gameConfig: GameConfig;
  // Override specific components if needed
  HeaderComponent?: React.ComponentType<any>;
  FooterComponent?: React.ComponentType<any>;
  MilestoneComponent?: React.ComponentType<any>;
  StartGameModalComponent?: React.ComponentType<any>;

  // Custom handlers
  onMilestonePress?: (
    status: string,
    number: number,
    levelName?: string,
  ) => void;
  onLevelSelect?: (level: any) => void;

  // Additional props
  customStyles?: any;
  customHooks?: {
    useSVGPath?: (params: any) => any;
    usePathTrail?: (params: any) => any;
  };
}

// Utility function to get game config
export const getGameConfig = (gameId: string): GameConfig => {
  const config = GAME_CONFIGS[gameId];
  if (!config) {
    showSnackbar({
      message: 'Trò chơi này đang được phát triển',
      status: ComponentStatus.INFOR,
    });
    return getGameConfig('ALTP');
    // throw new Error(`Game config not found for gameId: ${gameId}`);
  }
  return config;
};

// Utility function to merge custom config with default
export const mergeGameConfig = (
  gameId: string,
  customConfig: Partial<GameConfig>,
): GameConfig => {
  const defaultConfig = getGameConfig(gameId);
  return {
    ...defaultConfig,
    ...customConfig,
    // Deep merge for nested objects
    milestoneImages: {
      ...defaultConfig.milestoneImages,
      ...customConfig.milestoneImages,
    },
    footerIcons: {
      ...defaultConfig.footerIcons,
      ...customConfig.footerIcons,
    },
    colors: {
      ...defaultConfig.colors,
      ...customConfig.colors,
    },
  };
};

// // Responsive utilities
// const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
// const scale = SCREEN_WIDTH / 375;

// export function normalize(size: number): number {
//   const newSize = size * scale;
//   if (Platform.OS === 'ios') {
//     return Math.round(PixelRatio.roundToNearestPixel(newSize));
//   }
//   return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
// }

// export function wp(percentage: number): number {
//   return (percentage * SCREEN_WIDTH) / 100;
// }

// export function hp(percentage: number): number {
//   return (percentage * SCREEN_HEIGHT) / 100;
// }

// Calculate scale for milestones
// const calculateScale = (top: number) => {
//   const minTop = 0.55;
//   const maxTop = 0.85;
//   const baseScale = SCREEN_WIDTH / 375;
//   const minScale = 0.6 * baseScale;
//   const maxScale = 1.1 * baseScale;
//   const normalized = (top - minTop) / (maxTop - minTop);
//   return minScale + normalized * (maxScale - minScale);
// };

// Generic Milestone Component Props
// interface GenericMilestoneProps {
//   status: 'completed' | 'in-progress' | 'locked';
//   number: number;
//   levelName?: string;
//   onPress: () => void;
//   scaleFactor: number;
//   gameConfig: GameConfig;
// }

// // Generic Milestone Component
// const GenericMilestoneComponent: React.FC<GenericMilestoneProps> = ({
//   status,
//   number,
//   onPress,
//   scaleFactor,
//   gameConfig,
// }) => {
//   const scaleAnim = useRef(new Animated.Value(scaleFactor)).current;
//   const [isAnimating, setIsAnimating] = useState(false);

//   const getMilestoneImage = (status: string) => {
//     switch (status) {
//       case 'completed':
//         return gameConfig.milestoneImages.completed;
//       case 'in-progress':
//         return gameConfig.milestoneImages.inProgress;
//       default:
//         return gameConfig.milestoneImages.locked;
//     }
//   };

//   const handlePress = () => {
//     if (isAnimating) return;

//     setIsAnimating(true);
//     Animated.sequence([
//       Animated.timing(scaleAnim, {
//         toValue: scaleFactor * 1.2,
//         duration: 100,
//         useNativeDriver: true,
//       }),
//       Animated.timing(scaleAnim, {
//         toValue: scaleFactor,
//         duration: 100,
//         useNativeDriver: true,
//       }),
//     ]).start(() => {
//       setIsAnimating(false);
//       onPress();
//     });
//   };

//   return (
//     <View style={{position: 'relative'}}>
//       <TouchableWithoutFeedback onPress={handlePress}>
//         <Animated.View
//           style={[styles.milestoneContent, {transform: [{scale: scaleAnim}]}]}>
//           <FastImage
//             source={getMilestoneImage(status)}
//             style={styles.milestoneImage}
//             resizeMode={FastImage.resizeMode.contain}
//           />
//           <Text style={[styles.milestoneNumber, { color: gameConfig.colors.primary }]}>
//             {number}
//           </Text>
//         </Animated.View>
//       </TouchableWithoutFeedback>
//     </View>
//   );
// };
