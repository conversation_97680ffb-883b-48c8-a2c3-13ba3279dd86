import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Alert,
  Dimensions,
  PanResponder,
  Animated,
  Easing,
  Vibration,
  Platform,
  ImageBackground,
} from 'react-native';

import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

import {GestureHandlerRootView} from 'react-native-gesture-handler';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface InsertZoneProps {
  onReceiveDragDrop: (word: string) => void;
  index: number;
  isActive?: boolean;
  onDropSuccess?: () => void;
}

const InsertZone = React.forwardRef<View, InsertZoneProps>(
  ({isActive, onDropSuccess}, ref) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const opacityAnim = useRef(new Animated.Value(0.3)).current;
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const successScaleAnim = useRef(new Animated.Value(1)).current;

    // Animation for when zone becomes active (word being dragged over)
    React.useEffect(() => {
      if (isActive) {
        // Start hover animations with smoother easing
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1.3,
            duration: 150,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          // Smoother pulse animation
          Animated.loop(
            Animated.sequence([
              Animated.timing(pulseAnim, {
                toValue: 1.05,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
              Animated.timing(pulseAnim, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
            ]),
          ),
        ]).start();
      } else {
        // Reset to normal state with smooth transition
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0.4,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
        ]).start();

        // Stop pulse animation smoothly
        pulseAnim.stopAnimation();
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 100,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }).start();
      }
    }, [isActive, scaleAnim, opacityAnim, pulseAnim]);

    // Success animation when word is dropped
    const triggerSuccessAnimation = React.useCallback(() => {
      // Add haptic feedback
      Vibration.vibrate(50, false);

      Animated.sequence([
        // Quick scale up with bounce
        Animated.timing(successScaleAnim, {
          toValue: 1.4,
          duration: 120,
          easing: Easing.out(Easing.back(1.5)),
          useNativeDriver: true,
        }),
        // Smooth scale back with elastic feel
        Animated.timing(successScaleAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.elastic(1.2)),
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (onDropSuccess) {
          onDropSuccess();
        }
      });
    }, [successScaleAnim, onDropSuccess]);

    // Store the success animation function in a ref so parent can access it
    const successAnimRef = useRef(triggerSuccessAnimation);
    successAnimRef.current = triggerSuccessAnimation;

    // Expose success animation to parent via a custom property
    React.useEffect(() => {
      if (ref && typeof ref === 'object' && ref.current) {
        (ref.current as any).triggerSuccessAnimation = successAnimRef.current;
      }
    });

    return (
      <View ref={ref} style={styles.insertZone}>
        <Animated.View
          style={[
            styles.insertZoneInner,
            isActive && styles.insertZoneActive,
            {
              transform: [
                {scale: Animated.multiply(scaleAnim, successScaleAnim)},
                {scale: pulseAnim},
              ],
              opacity: opacityAnim,
            },
          ]}>
          {/* Hiển thị khoảng trắng thay vì dấu + */}
        </Animated.View>
      </View>
    );
  },
);

// Draggable Word Component in sentence - can be dragged to reorder
interface DraggableWordInSentenceProps {
  word: string;
  index: number;
  isSubmitted: boolean;
  isInserting: boolean;
  insertIndex: number;
  onDragStart: (word: string, index: number) => void;
  onDragMove: (x: number, y: number) => void;
  onDragEnd: (word: string, fromIndex: number, x: number, y: number) => void;
  isDragging: boolean;
  dragId?: string; // Unique identifier for this word instance
}

const DraggableWordInSentence = React.memo(
  ({
    word,
    index,
    isSubmitted,
    isInserting,
    insertIndex,
    onDragStart,
    onDragMove,
    onDragEnd,
    isDragging,
  }: DraggableWordInSentenceProps) => {
    const translateX = useRef(new Animated.Value(0)).current;
    const scale = useRef(new Animated.Value(1)).current;
    const pan = useRef(new Animated.ValueXY()).current;
    const dragScale = useRef(new Animated.Value(1)).current;

    // Animation for sentence expansion
    React.useEffect(() => {
      if (isDragging) {
        // Don't animate position when this word is being dragged
        return;
      }

      if (isInserting) {
        // Animate words to make space for new word
        const shouldMoveRight = index >= insertIndex;
        const moveDistance = shouldMoveRight ? 60 : 0; // Approximate word width + margin

        Animated.parallel([
          Animated.timing(translateX, {
            toValue: moveDistance,
            duration: 300,
            easing: Easing.out(Easing.back(1.1)),
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.timing(scale, {
              toValue: 0.95,
              duration: 150,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
            Animated.timing(scale, {
              toValue: 1,
              duration: 150,
              easing: Easing.out(Easing.back(1.2)),
              useNativeDriver: true,
            }),
          ]),
        ]).start();
      } else {
        // Reset position
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: 0,
            duration: 250,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
        ]).start();
      }
    }, [isInserting, insertIndex, index, translateX, scale, isDragging]);

    // Pan responder for dragging words in sentence - recreate on each render to ensure fresh state
    const panResponder = React.useMemo(
      () =>
        PanResponder.create({
          onStartShouldSetPanResponder: () => {
            console.log(
              `🔥 onStartShouldSetPanResponder for word "${word}" at index ${index}, isSubmitted=${isSubmitted}`,
            );
            return !isSubmitted;
          },
          onMoveShouldSetPanResponder: () => {
            console.log(
              `🔥 onMoveShouldSetPanResponder for word "${word}" at index ${index}, isSubmitted=${isSubmitted}`,
            );
            return !isSubmitted;
          },
          onPanResponderGrant: () => {
            console.log(
              `🔥 onPanResponderGrant for word "${word}" at index ${index}`,
            );
            onDragStart(word, index);
            Animated.spring(dragScale, {
              toValue: 1.1,
              useNativeDriver: true,
            }).start();
          },
          onPanResponderMove: (_, gestureState) => {
            pan.setValue({x: gestureState.dx, y: gestureState.dy});
            onDragMove(gestureState.moveX, gestureState.moveY);
          },
          onPanResponderRelease: (_, gestureState) => {
            Animated.spring(dragScale, {
              toValue: 1,
              useNativeDriver: true,
            }).start();
            onDragEnd(word, index, gestureState.moveX, gestureState.moveY);
            pan.setValue({x: 0, y: 0});
          },
          onPanResponderTerminate: () => {
            Animated.spring(dragScale, {
              toValue: 1,
              useNativeDriver: true,
            }).start();
            pan.setValue({x: 0, y: 0});
          },
        }),
      [
        word,
        index,
        isSubmitted,
        onDragStart,
        onDragMove,
        onDragEnd,
        dragScale,
        pan,
      ],
    );

    return (
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          styles.word,
          isSubmitted && styles.wordSubmitted,
          isDragging && styles.wordDragging,
          {
            transform: [
              {translateX: isDragging ? pan.x : translateX},
              {translateY: isDragging ? pan.y : 0},
              {scale: Animated.multiply(scale, dragScale)},
            ],
            zIndex: isDragging ? 1000 : 1,
          },
        ]}>
        <Text
          style={[styles.wordText, isSubmitted && styles.wordTextSubmitted]}>
          {word}
        </Text>
      </Animated.View>
    );
  },
);

interface DraggableWordProps {
  word: string;
  used: boolean;
  onDragEnd: (word: string, x: number, y: number) => void;
  onDragStart: () => void;
  onDragMove: (x: number, y: number) => void;
}

const DraggableWord = React.memo(
  ({word, used, onDragEnd, onDragStart, onDragMove}: DraggableWordProps) => {
    const pan = useRef(new Animated.ValueXY()).current;
    const scale = useRef(new Animated.Value(1)).current;

    const panResponder = useRef(
      PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: () => {
          //console.log('Drag started');
          onDragStart();
          Animated.spring(scale, {
            toValue: 1.1,
            useNativeDriver: true,
          }).start();
        },
        onPanResponderMove: (_, gestureState) => {
          //console.log('Dragging:', gestureState.moveX, gestureState.moveY);
          pan.setValue({x: gestureState.dx, y: gestureState.dy});
          onDragMove(gestureState.moveX, gestureState.moveY);
        },
        onPanResponderRelease: (_, gestureState) => {
          //console.log('Drag released at:', gestureState.moveX, gestureState.moveY);
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          onDragEnd(word, gestureState.moveX, gestureState.moveY);
          pan.setValue({x: 0, y: 0});
        },
        onPanResponderTerminate: () => {
          console.log('Drag terminated');
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          pan.setValue({x: 0, y: 0});
        },
      }),
    ).current;

    if (used) {
      return (
        <View style={[styles.draggable, styles.usedDraggable]}>
          <Text style={styles.dragText}>{word}</Text>
        </View>
      );
    }

    return (
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          styles.draggable,
          {
            transform: [{translateX: pan.x}, {translateY: pan.y}, {scale}],
          },
        ]}>
        <Text style={styles.dragText}>{word}</Text>
      </Animated.View>
    );
  },
);

interface InsertZonePosition {
  x: number;
  y: number;
  index: number;
  distance?: number;
}

const SakuXayTo = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const {totalLives, currentLives, isGameOver, messageGameOver, gem, cup} =
    useSelector((state: RootState) => state.gameStore);
  const {totalQuestion, questionDone, currentQuestion, isWinLevel} =
    useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);

  const [sentence, setSentence] = useState(['Tôi', 'ăn', 'cơm']);
  const [draggableWords, setDraggableWords] = useState([
    'đang',
    'sẽ',
    'đã',
    'rất',
    'ngon',
  ]);
  const [usedWords, setUsedWords] = useState<Set<string>>(new Set());
  const [insertZones, setInsertZones] = useState<InsertZonePosition[]>([]);
  const [activeZoneIndex, setActiveZoneIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isInserting, setIsInserting] = useState(false);
  const [insertingAtIndex, setInsertingAtIndex] = useState<number>(-1);
  const [draggingWordFromSentence, setDraggingWordFromSentence] = useState<{
    word: string;
    index: number;
    uniqueId: string; // Unique identifier for this specific word instance
  } | null>(null);
  const [currentlyDraggingWord, setCurrentlyDraggingWord] = useState<
    string | null
  >(null);

  const insertZoneRefs = useRef<View[]>([]);
  const insertZonesRef = useRef<InsertZonePosition[]>([]);
  const currentSentenceRef = useRef<string[]>(sentence);
  const dragOperationRef = useRef<{word: string; index: number} | null>(null);

  // Animation refs for sentence expansion
  const sentenceContainerHeight = useRef(new Animated.Value(80)).current;

  // Update sentence ref when sentence changes
  React.useEffect(() => {
    currentSentenceRef.current = sentence;
  }, [sentence]);

  const updateInsertZonePositions = useCallback(() => {
    const newZones: InsertZonePosition[] = [];
    let completed = 0;

    insertZoneRefs.current.forEach((ref, index) => {
      if (ref) {
        ref.measure((x, y, width, height, pageX, pageY) => {
          // Tính toán vị trí chính xác của insert zone
          const centerX = pageX + width / 2;
          const centerY = pageY + height / 2;

          newZones[index] = {
            x: centerX,
            y: centerY,
            index,
          };
          completed++;

          if (completed === insertZoneRefs.current.length) {
            console.log('Updated insert zones:', newZones);
            insertZonesRef.current = newZones;
            setInsertZones(newZones);
          }
        });
      }
    });
  }, []);

  // Cập nhật vị trí khi component mount và khi sentence thay đổi
  React.useLayoutEffect(() => {
    console.log('Layout effect running');
    // Thêm delay nhỏ để đảm bảo layout đã được cập nhật
    const timer = setTimeout(() => {
      updateInsertZonePositions();
    }, 100);
    return () => clearTimeout(timer);
  }, [sentence, updateInsertZonePositions]);

  const handleInsert = useCallback(
    (index: number, word: string) => {
      console.log('=== INSERT WORD ===');
      console.log('Word:', word);
      console.log('At index:', index);
      console.log('Current sentence:', sentence);

      if (usedWords.has(word)) {
        console.log('Word already used:', word);
        return;
      }
      if (sentence.length >= 10) {
        console.log('Sentence too long');
        return;
      }

      // Sử dụng functional update để đảm bảo cập nhật dựa trên state mới nhất
      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        newSentence.splice(index, 0, word);
        console.log('New sentence:', newSentence);
        return newSentence;
      });

      setUsedWords(prev => new Set([...prev, word]));
      setDraggableWords(prev => prev.filter(w => w !== word));
    },
    [usedWords],
  );

  const handleDragStart = useCallback(() => {
    setActiveZoneIndex(null);
  }, []);

  // Handle drag start for words in sentence
  const handleSentenceWordDragStart = useCallback(
    (word: string, index: number) => {
      console.log('🚀 DRAG START CALLED:', word, 'at index:', index);
      console.log(
        '🚀 Current currentlyDraggingWord before:',
        currentlyDraggingWord,
      );

      const uniqueId = `${Date.now()}-${Math.random()}`;
      setDraggingWordFromSentence({word, index, uniqueId});
      setCurrentlyDraggingWord(word);
      setActiveZoneIndex(null);

      console.log('🚀 Set currentlyDraggingWord to:', word);
      console.log('🚀 draggingWordFromSentence set to:', {
        word,
        index,
        uniqueId,
      });
    },
    [],
  );

  // Handle drag end for words in sentence
  const handleSentenceWordDragEnd = useCallback(
    (word: string, fromIndex: number, x: number, y: number) => {
      console.log('=== SENTENCE WORD DRAG END ===');
      console.log('Word:', word, 'FromIndex:', fromIndex);
      console.log('Drop position:', x, y);
      console.log('Current sentence before move:', currentSentenceRef.current);

      const zones = insertZonesRef.current;

      if (zones.length === 0) {
        console.log('No zones available');
        setDraggingWordFromSentence(null);
        return;
      }

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      console.log('Closest zone:', closestZone);

      if (closestZone.distance && closestZone.distance < 80) {
        let targetIndex = closestZone.index;
        console.log('Target index before adjustment:', targetIndex);

        // Only move if target position is different from current position
        if (targetIndex !== fromIndex && targetIndex !== fromIndex + 1) {
          const currentSentence = currentSentenceRef.current;

          // Find the current position of the word
          let actualFromIndex = -1;

          // First, try the original index if it's still valid
          if (
            fromIndex >= 0 &&
            fromIndex < currentSentence.length &&
            currentSentence[fromIndex] === word
          ) {
            actualFromIndex = fromIndex;
          } else {
            // If original index doesn't work, find the word in the sentence
            // For duplicate words, we need a better strategy
            actualFromIndex = currentSentence.findIndex(w => w === word);

            if (actualFromIndex === -1) {
              console.error(
                'Word not found in sentence:',
                word,
                'Current sentence:',
                currentSentence,
              );
              setDraggingWordFromSentence(null);
              return;
            }
          }

          console.log(
            'Original fromIndex:',
            fromIndex,
            'Actual fromIndex:',
            actualFromIndex,
          );

          // Use the actual current position
          fromIndex = actualFromIndex;

          setSentence(prevSentence => {
            console.log('Previous sentence:', prevSentence);

            // Double check the word is still at the expected position
            if (prevSentence[fromIndex] !== word) {
              console.error(
                'Word not found at expected position during setState',
              );
              return prevSentence; // Don't change anything
            }

            const newSentence = [...prevSentence];

            // Remove word from current position
            const removedWord = newSentence.splice(fromIndex, 1)[0];
            console.log('Removed word:', removedWord);

            // Adjust target index if it's after the removed word
            if (targetIndex > fromIndex) {
              targetIndex -= 1;
            }

            // Validate target index
            if (targetIndex < 0) targetIndex = 0;
            if (targetIndex > newSentence.length)
              targetIndex = newSentence.length;

            // Insert word at new position
            newSentence.splice(targetIndex, 0, removedWord);
            console.log('Final target index:', targetIndex);
            console.log('New sentence after move:', newSentence);

            return newSentence;
          });

          // Reset drag states immediately after successful move
          console.log('✅ SUCCESSFUL MOVE - Resetting all drag states');
          setDraggingWordFromSentence(null);
          setCurrentlyDraggingWord(null);
          setActiveZoneIndex(null);
          setIsInserting(false);
          setInsertingAtIndex(-1);
          console.log('✅ All drag states reset after successful move');

          // Update insert zone positions after sentence change
          setTimeout(() => {
            updateInsertZonePositions();
          }, 100);
        } else {
          console.log('Target position same as current, no move needed');
        }
      } else {
        // Check if dragged outside sentence area - remove from sentence
        const sentenceArea = {
          x: 50,
          y: 400, // Approximate sentence area
          width: SCREEN_WIDTH - 100,
          height: 200,
        };

        if (
          x < sentenceArea.x ||
          x > sentenceArea.x + sentenceArea.width ||
          y < sentenceArea.y ||
          y > sentenceArea.y + sentenceArea.height
        ) {
          console.log('Word dragged outside sentence area, removing');

          const currentSentence = currentSentenceRef.current;

          // Find the current position of the word
          let actualFromIndex = -1;

          // First, try the original index if it's still valid
          if (
            fromIndex >= 0 &&
            fromIndex < currentSentence.length &&
            currentSentence[fromIndex] === word
          ) {
            actualFromIndex = fromIndex;
          } else {
            // If original index doesn't work, find the word in the sentence
            actualFromIndex = currentSentence.findIndex(w => w === word);

            if (actualFromIndex === -1) {
              console.error(
                'Cannot find word to remove:',
                word,
                'Current sentence:',
                currentSentence,
              );
              setDraggingWordFromSentence(null);
              return;
            }
          }

          console.log('Removing word at actualFromIndex:', actualFromIndex);

          // Validate the found index
          if (
            actualFromIndex >= 0 &&
            actualFromIndex < currentSentence.length
          ) {
            setSentence(prevSentence => {
              const newSentence = [...prevSentence];
              newSentence.splice(actualFromIndex, 1);
              console.log('Sentence after removing word:', newSentence);
              return newSentence;
            });

            setDraggableWords(prev => [...prev, word]);
            setUsedWords(prev => {
              const newUsed = new Set(prev);
              newUsed.delete(word);
              return newUsed;
            });

            // Reset drag states immediately after removal
            setDraggingWordFromSentence(null);
            setCurrentlyDraggingWord(null);
            setActiveZoneIndex(null);
            setIsInserting(false);
            setInsertingAtIndex(-1);

            // Update insert zone positions after sentence change
            setTimeout(() => {
              updateInsertZonePositions();
            }, 100);
          } else {
            console.error(
              'Cannot remove word - invalid position or word mismatch',
            );
          }
        }
      }

      // Reset states immediately for cases where no movement occurred
      if (!closestZone.distance || closestZone.distance >= 80) {
        setDraggingWordFromSentence(null);
        setCurrentlyDraggingWord(null);
        setActiveZoneIndex(null);
        setIsInserting(false);
        setInsertingAtIndex(-1);
      }

      console.log('=== END SENTENCE WORD DRAG ===');
    },
    [SCREEN_WIDTH, updateInsertZonePositions],
  );

  const handleDragMove = useCallback(
    (x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      // Increased detection radius for smoother interaction
      const detectionRadius = 80;
      if (closestZone.distance && closestZone.distance < detectionRadius) {
        // If dragging a word from sentence, adjust the target index logic
        let targetIndex = closestZone.index;

        if (draggingWordFromSentence) {
          // When dragging from sentence, find the current position of the word
          const currentSentence = currentSentenceRef.current;
          const currentFromIndex = currentSentence.findIndex(
            w => w === draggingWordFromSentence.word,
          );

          if (currentFromIndex !== -1) {
            // Don't highlight the same position or adjacent positions
            if (
              targetIndex === currentFromIndex ||
              targetIndex === currentFromIndex + 1
            ) {
              if (activeZoneIndex !== null) {
                setActiveZoneIndex(null);
                setIsInserting(false);
                setInsertingAtIndex(-1);
              }
              return;
            }
          }
        }

        if (activeZoneIndex !== targetIndex) {
          setActiveZoneIndex(targetIndex);

          // Trigger word expansion animation only if not dragging from sentence at same/adjacent position
          if (targetIndex <= sentence.length) {
            setIsInserting(true);
            setInsertingAtIndex(targetIndex);

            // Animate sentence container height
            Animated.timing(sentenceContainerHeight, {
              toValue: 100, // Expand height
              duration: 200,
              easing: Easing.out(Easing.quad),
              useNativeDriver: false,
            }).start();
          }
        }
      } else {
        if (activeZoneIndex !== null) {
          setActiveZoneIndex(null);

          // Reset expansion animation
          setIsInserting(false);
          setInsertingAtIndex(-1);

          // Reset sentence container height
          Animated.timing(sentenceContainerHeight, {
            toValue: 80, // Original height
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: false,
          }).start();
        }
      }
    },
    [
      activeZoneIndex,
      sentence.length,
      sentenceContainerHeight,
      draggingWordFromSentence,
    ],
  );

  const handleDragEnd = useCallback(
    (word: string, x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      if (closestZone.distance && closestZone.distance < 50) {
        if (usedWords.has(word)) return;

        setSentence(prevSentence => {
          const newSentence = [...prevSentence];
          newSentence.splice(closestZone.index, 0, word);
          return newSentence;
        });

        setUsedWords(prev => new Set([...prev, word]));
        setDraggableWords(prev => prev.filter(w => w !== word));
      }

      setActiveZoneIndex(null);

      // Reset expansion animation after drop
      setIsInserting(false);
      setInsertingAtIndex(-1);

      // Reset sentence container height
      Animated.timing(sentenceContainerHeight, {
        toValue: 80,
        duration: 300,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: false,
      }).start();
    },
    [usedWords, sentenceContainerHeight],
  );

  const handleSubmit = useCallback(() => {
    setIsSubmitted(true);
  }, []);

  const resetSentence = useCallback(() => {
    setSentence(['Tôi', 'ăn', 'cơm']);
    setUsedWords(new Set());
    setDraggableWords(['đang', 'sẽ', 'đã', 'rất', 'ngon']);
    setIsSubmitted(false);

    // Reset animation states
    setIsInserting(false);
    setInsertingAtIndex(-1);
    setActiveZoneIndex(null);
    setDraggingWordFromSentence(null);
    setCurrentlyDraggingWord(null);

    // Reset sentence container height
    sentenceContainerHeight.setValue(80);
  }, [sentenceContainerHeight]);

  // Refs for drop zones positions

  const hiddenInputRef = useRef<TextInput | null>(null);

  useEffect(() => {
    startGame();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      if (hiddenInputRef.current) {
        hiddenInputRef.current.focus();
      }
    };
  }, []);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isWinLevel) {
      winGame();
    }
  }, [isWinLevel]);

  const startGame = () => {
    resetQuestion();
    gameHook.restartGame();
    dhbcHook.startGame();
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Thắng
  const winGame = () => {
    gameHook.setData({stateName: 'gem', value: gem + 30});
    gameHook.setData({stateName: 'cup', value: cup + 10});
  };

  // Reset câu hỏi
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
  };

  // Sử dụng gợi ý
  const useHint = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setShowModelConfirm(false);
    setShowHintModel(true);
  };
  return (
    <ImageBackground
      source={require('./assets/backgroundGame.png')}
      style={styles.backgroundImage}
      resizeMode="cover">
      <SafeAreaView edges={['top']} />

      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Lives
                totalLives={totalLives}
                currentLives={currentLives}></Lives>
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}>
          <View style={styles.questionContainer}>
            <Text style={styles.text}>{currentQuestion?.text}</Text>
          </View>
          <GestureHandlerRootView style={{flex: 1}}>
            <View style={styles.container}>
              <View style={styles.mainContent}>
                <Animated.View style={[styles.plateContainer]}>
                  <ImageBackground
                    source={require('./assets/plate.png')}
                    style={styles.plateBackground}
                    resizeMode="stretch">
                    <View style={styles.row}>
                      {sentence.map((word, index) => {
                        const wordId = `${word}-${index}`;
                        return (
                          <View key={`slot-${index}`} style={styles.slotWrap}>
                            <InsertZone
                              ref={ref => {
                                if (ref) {
                                  insertZoneRefs.current[index] = ref;
                                }
                              }}
                              index={index}
                              onReceiveDragDrop={word =>
                                handleInsert(index, word)
                              }
                              isActive={activeZoneIndex === index}
                              onDropSuccess={() =>
                                console.log(`Word dropped at index ${index}`)
                              }
                            />
                            <DraggableWordInSentence
                              word={word}
                              index={index}
                              isSubmitted={isSubmitted}
                              isInserting={isInserting}
                              insertIndex={insertingAtIndex}
                              onDragStart={handleSentenceWordDragStart}
                              onDragMove={handleDragMove}
                              onDragEnd={handleSentenceWordDragEnd}
                              isDragging={(() => {
                                const isDrag = currentlyDraggingWord === word;
                                if (currentlyDraggingWord !== null) {
                                  console.log(
                                    `🎯 Word "${word}" at index ${index}: isDragging=${isDrag}, currentlyDraggingWord="${currentlyDraggingWord}"`,
                                  );
                                }
                                return isDrag;
                              })()}
                              dragId={wordId}
                            />
                          </View>
                        );
                      })}
                      <InsertZone
                        ref={ref => {
                          if (ref) {
                            insertZoneRefs.current[sentence.length] = ref;
                          }
                        }}
                        index={sentence.length}
                        onReceiveDragDrop={word =>
                          handleInsert(sentence.length, word)
                        }
                        isActive={activeZoneIndex === sentence.length}
                        onDropSuccess={() => console.log(`Word dropped at end`)}
                      />
                    </View>
                  </ImageBackground>
                </Animated.View>
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.button, styles.submitButton]}
                    onPress={handleSubmit}
                    disabled={isSubmitted}>
                    <Text style={styles.buttonText}>Kiểm tra đáp án</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.wordBank}>
                  {draggableWords.map((word, index) => (
                    <DraggableWord
                      key={`drag-${index}-${word}`}
                      word={word}
                      used={usedWords.has(word)}
                      onDragEnd={handleDragEnd}
                      onDragStart={handleDragStart}
                      onDragMove={handleDragMove}
                    />
                  ))}
                </View>
              </View>
            </View>
          </GestureHandlerRootView>

          <View>
            <BottomGame resetGame={resetSentence} />
          </View>
        </View>
        <View style={{zIndex: 1000}}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion.hint}
          />
          <GameOverModal
            visible={isGameOver}
            onClose={() => {}}
            restartGame={startGame}
            message={messageGameOver}
            isTimeOut={false}
          />
          <ModelDoneLevel
            visible={isWinLevel}
            onNextLevel={startGame}
            currentGem={gem - 30}
            currentCup={cup - 10}
            gemAdd={30}
            cupAdd={10}
          />
        </View>
      </View>
      <SafeAreaView edges={['bottom']} />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  mainContent: {
    flex: 1,
    width: '100%',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  questionContainer: {
    width: Dimensions.get('window').width - 32,
    height: undefined,
    minHeight: 65,
    marginBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    backgroundColor: '#FCF8E8',
    justifyContent: 'center',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateContainer: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateBackground: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },

  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    padding: 16,
  },
  slotWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    flexShrink: 0,
  },
  insertZone: {
    width: 2,
    height: 2,
    marginHorizontal: 4,
  },
  insertZoneInner: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#dee2e6',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  word: {
    padding: 8,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 9,
  },
  wordText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
    textAlign: 'center',
  },
  wordSubmitted: {
    backgroundColor: '#c8e6c9',
    borderColor: '#4caf50',
  },
  wordTextSubmitted: {
    color: '#2e7d32',
  },
  wordBank: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
    padding: 10,
    width: '100%',
  },
  draggable: {
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
  },
  dragText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
    width: '100%',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 40,
  },
  resetButton: {
    backgroundColor: '#AE2B26',
  },
  submitButton: {
    backgroundColor: '#AE2B26',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  usedDraggable: {
    backgroundColor: '#bdbdbd',
    opacity: 0.5,
  },
  wordDragging: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4caf50',
    shadowColor: '#4caf50',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  insertZoneActive: {
    backgroundColor: '#e3f2fd',
    borderWidth: 3,
    borderColor: '#112164',
    borderStyle: 'solid',
    shadowColor: '#112164',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 8,
  },
  gameContent: {
    marginTop: 16,
    flex: 1,
    alignItems: 'center',
  },
});

export default SakuXayTo;
