import {useDispatch} from 'react-redux';
import {
  nextQuestion,
  startGame,
  setData,
} from '../../reducers/game/dhbcReducer';

export const useDhbcHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
  };

  return action;
};
