import {createSlice} from '@reduxjs/toolkit';

interface Question {
  id: string;
  text: string;
  image: string;
  hint: string;
  answer: string;
}

interface State {
  listQuestion: Question[];
  currentQuestion: Question;
  totalQuestion: number;
  questionDone: number;
  isWinLevel: boolean;
}

const data = [
  {
    id: '1',
    text: 'Đ<PERSON>y là con gì',
    image:
      'https://pethouse.com.vn/wp-content/uploads/2023/06/cho-pug-1256x800.webp',
    hint: 'Đ<PERSON>y là một loài vật có vú và có thể ăn thịt',
    answer: 'con chó',
  },
  {
    id: '2',
    text: 'Đ<PERSON>y là con gì',
    image:
      'https://topanh.com/wp-content/uploads/2024/02/Hinh-anh-con-khi-dep-nhat.jpg',
    hint: 'Đ<PERSON>y là một loài động vật có thể leo trèo',
    answer: 'con khỉ',
  },
];
const initialState: State = {
  listQuestion: [],
  currentQuestion: {} as Question,
  totalQuestion: 0,
  questionDone: 0,
  isWinLevel: false,
};

export const dhbcReducer = createSlice({
  name: 'dhbcReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame(state) {
      state.listQuestion = data;
      state.currentQuestion = data[0];
      state.totalQuestion = data.length;
      state.questionDone = 0;
      state.isWinLevel = false;
    },
    nextQuestion(state) {
      if (state.questionDone === state.totalQuestion) {
        state.isWinLevel = true;
        return;
      }
      state.currentQuestion = state.listQuestion[state.questionDone];
    },
    reset(state) {},
  },
});

export const {setData, reset, startGame, nextQuestion} = dhbcReducer.actions;

export default dhbcReducer.reducer;
