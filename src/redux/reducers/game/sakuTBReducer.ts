import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import { SakuTBDA } from '../../../modules/game/sakutimban/da/sakuTBDA';
import {
  SakuTBGameState,
  GetGameConfigRequest,
  GameConfig,
} from '../../../modules/game/sakutimban/types/sakuTBTypes';
import {
  transformQuestions,
  shuffleArray,
  validateTransformedQuestion,
} from '../../../modules/game/sakutimban/utils/sakuTBUtils';
import {
  initializeFallbackData,
} from '../../../modules/game/sakutimban/data/fallbackData';

// Legacy Item interface for backward compatibility
export interface Item {
  id: string;
  text: string;
  matchId: string;
  type: 'audio' | 'image' | 'text';
}

// Async thunk để load questions từ API
export const loadGameQuestions = createAsyncThunk(
  'sakuTB/loadQuestions',
  async ({ gameId, stage, competenceId }: { gameId: string; stage: number, competenceId: string }) => {
    try {
      console.log(`[Redux] Loading questions for GameId: ${gameId}, Stage: ${stage}`);
      const sakuTBDA = new SakuTBDA();
      const rawQuestions = await sakuTBDA.getQuestionsByGameAndStage(gameId, stage, competenceId);
debugger;

      if (rawQuestions.length === 0) {
        console.warn('[Redux] No questions found, using fallback data');
        const fallbackData = initializeFallbackData();
        return {
          rawQuestions: fallbackData.questions,
          transformedQuestions: transformQuestions(fallbackData.questions),
          isFromAPI: false
        };
      }

      const transformedQuestions = transformQuestions(rawQuestions);

      // Validate transformed questions
      const validQuestions = transformedQuestions.filter(validateTransformedQuestion);

      if (validQuestions.length === 0) {
        throw new Error('No valid questions after transformation');
      }

      console.log(`[Redux] Successfully loaded ${validQuestions.length} questions`);

      return {
        rawQuestions,
        transformedQuestions: validQuestions,
        isFromAPI: true
      };
    } catch (error) {
      console.error('[Redux] Error loading questions:', error);
      // Fallback to local data
      const fallbackData = initializeFallbackData();
      return {
        rawQuestions: fallbackData.questions,
        transformedQuestions: transformQuestions(fallbackData.questions),
        isFromAPI: false
      };
    }
  }
);


// Async thunk để load GameConfig từ API
export const loadGameConfig = createAsyncThunk(
  'sakuTB/loadGameConfig',
  async ({ gameId }: GetGameConfigRequest) => {
    try {
      const gameConfig = await SakuTBDA.getGameConfig(gameId);
     if(gameConfig){
        return gameConfig;
      }
    } catch (error) {
      return {
          gameId: gameId,
          scorePerLife: 10,
          maxLives: 3,
          timeLimit: 300,
          bonusScore: 50,
          isActive: true,
      };
    }
  }
);

// New state interface using SakuTBGameState
const initialState: SakuTBGameState = {
  // API Data
  rawQuestions: [],
  questions: [],
  gameConfig: null,

  // Current Question
  currentQuestionIndex: 0,
  currentQuestion: null,

  // Game Items (current question)
  listItemsLeft: [],
  listItemsRight: [],

  // Game State
  selectedLeft: null,
  selectedRight: null,
  matchedPairs: [],
  errorPairs: [],
  listItemsDone: [],

  // Progress
  questionDone: 0,
  totalQuestion: 0,
  currentStage: 1,

  // Scoring & Config (from GameConfig API)
  maxLives: 3,
  currentLives: 3,
  timeLimit: 300,
  timeRemaining: 300,
  scorePerLife: 10,
  bonusScore: 50,
  currentScore: 0,

  // API State
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,
};

export const SakuTBReducer = createSlice({
  name: 'SakuTBReducer',
  initialState,
  reducers: {
    setData(state, action: { payload: { stateName: keyof SakuTBGameState; value: any } }) {
      (state as any)[action.payload.stateName] = action.payload.value;
    },
    reset: state => {
      // Reset to initial state but keep API data and config
      state.currentQuestionIndex = 0;
      state.currentQuestion = state.questions.length > 0 ? state.questions[0] : null;
      state.listItemsLeft = state.currentQuestion ? shuffleArray([...state.currentQuestion.leftItems]) : [];
      state.listItemsRight = state.currentQuestion ? shuffleArray([...state.currentQuestion.rightItems]) : [];
      state.selectedLeft = null;
      state.selectedRight = null;
      state.matchedPairs = [];
      state.errorPairs = [];
      state.listItemsDone = [];
      state.questionDone = 0;
      state.currentLives = state.maxLives;
      state.timeRemaining = state.timeLimit;
      state.currentScore = 0;
    },
    applyGameConfig: (state, action: { payload: GameConfig }) => {
      const config = action.payload;
      state.gameConfig = config;
      state.maxLives = config.maxLives;
      state.currentLives = config.maxLives;
      state.timeLimit = config.timeLimit;
      state.timeRemaining = config.timeLimit;
      state.scorePerLife = config.scorePerLife;
      state.bonusScore = config.bonusScore;
      state.configInitialized = true;

      console.log('[Redux] Game config applied:', {
        maxLives: config.maxLives,
        timeLimit: config.timeLimit,
        scorePerLife: config.scorePerLife,
        bonusScore: config.bonusScore,
      });
    },
    calculateFinalScore: state => {
      if (state.gameConfig) {
        const finalScore = SakuTBDA.calculateScore(
          state.gameConfig,
          state.currentLives,
          state.maxLives
        );
        state.currentScore = finalScore;

        console.log('[Redux] Final score calculated:', finalScore);
      }
    },
    nextQuestion: state => {
      if (state.currentQuestionIndex < state.questions.length - 1) {
        state.currentQuestionIndex += 1;
        state.currentQuestion = state.questions[state.currentQuestionIndex];
        state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
        state.listItemsRight = shuffleArray([...state.currentQuestion.rightItems]);
        state.selectedLeft = null;
        state.selectedRight = null;
        state.matchedPairs = [];
        state.errorPairs = [];
        state.listItemsDone = [];
      }
    },
    initializeGame: state => {
      if (state.questions.length > 0) {
        state.currentQuestionIndex = 0;
        state.currentQuestion = state.questions[0];
        state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
        state.listItemsRight = shuffleArray([...state.currentQuestion.rightItems]);
        state.totalQuestion = state.questions.length;
        state.initialized = true;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadGameQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.initialized = false;
      })
      .addCase(loadGameQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.rawQuestions = action.payload.rawQuestions;
        state.questions = action.payload.transformedQuestions;
        state.totalQuestion = action.payload.transformedQuestions.length;
        // Initialize first question
        if (action.payload.transformedQuestions.length > 0) {
          state.currentQuestionIndex = 0;
          state.currentQuestion = action.payload.transformedQuestions[0];
          state.listItemsLeft = shuffleArray([...state.currentQuestion.leftItems]);
          state.listItemsRight = shuffleArray([...state.currentQuestion.rightItems]);
          state.initialized = true;
        }
      })
      .addCase(loadGameQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load questions';
        console.error('[Redux] Failed to load questions:', action.error);
      })
      // GameConfig loading
      .addCase(loadGameConfig.pending, (state) => {
        state.configLoading = true;
        state.configError = null;
        state.configInitialized = false;
      })
      .addCase(loadGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.gameConfig = action.payload;

        // Apply config to game state
        const config = action.payload;
        state.maxLives = config.maxLives;
        state.currentLives = config.maxLives;
        state.timeLimit = config.timeLimit;
        state.timeRemaining = config.timeLimit;
        state.scorePerLife = config.scorePerLife;
        state.bonusScore = config.bonusScore;
        state.configInitialized = true;
      })
      .addCase(loadGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError = action.error.message || 'Failed to load game config';
        console.error('[Redux] Failed to load game config:', action.error);
      });
  },
});

export const {setData, reset, nextQuestion, initializeGame, applyGameConfig, calculateFinalScore} = SakuTBReducer.actions;

export default SakuTBReducer.reducer;
